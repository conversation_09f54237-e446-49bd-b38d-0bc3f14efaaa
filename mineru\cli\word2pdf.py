# Copyright (c) Opendatalab. All rights reserved.
# -*- coding: utf-8 -*-

import subprocess
import platform
from pathlib import Path
from loguru import logger
import os
import time

def convert_doc_to_pdf(input_file):
    """
    将word文件转换为pdf文件
    
    Args:
        input_file: word文件路径
    
    Returns:
        bytes: PDF文件的字节数据，如果转换失败返回None
    """
    if not isinstance(input_file, Path):
        input_file = Path(input_file)
    
    output_path = input_file.parent
    pdf_file = output_path / f"{input_file.stem}.pdf"
    
    try:  
        logger.info(f"开始使用LibreOffice转换Word文档")
        
        # 根据系统选择命令
        if platform.system().lower() == "windows":
            libreoffice_cmd = "soffice"
        else:
            libreoffice_cmd = "libreoffice"
        
        # 如果需要获取当前目录，使用这种方法：
        current_dir = os.getcwd()
        logger.info(f"当前工作目录: {current_dir}")

        # 使用LibreOffice转换
        cmd = [
            libreoffice_cmd,
            "--headless",
            "--convert-to",
            "pdf",
            "--outdir",
            str(output_path),
            str(input_file)
        ]
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            if pdf_file.exists():
                logger.info(f"✓ 转换成功")
                
                # 读取PDF文件
                with open(str(pdf_file), "rb") as pdf_file_handle:
                    pdf_bytes = pdf_file_handle.read()

                return pdf_bytes
            else:
                logger.error(f"✗ 转换失败:PDF文件未生成")
                return None
        else:
            logger.error(f"✗ 转换失败:命令执行失败")
            logger.error(f"错误信息: {result.stderr}")
            return None
                
    except subprocess.TimeoutExpired:
        logger.error(f"✗ 转换超时")
        return None
    except Exception as e:
        logger.error(f"✗ 转换出错: {e}")
        return None
