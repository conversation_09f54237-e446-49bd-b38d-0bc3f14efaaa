boto3>=1.28.43
click>=8.1.7
loguru>=0.7.2
numpy>=1.21.6
pdfminer.six==20250506
tqdm>=4.67.1
requests
httpx
pillow>=11.0.0
pypdfium2>=4.30.0
pypdf>=5.6.0
reportlab
pdftext>=0.6.2
modelscope>=1.26.0
huggingface-hub>=0.32.4
json-repair>=0.46.2

[all]
mineru[core]
mineru[sglang]

[api]
fastapi
python-multipart
uvicorn

[core]
mineru[vlm]
mineru[pipeline]
mineru[api]
mineru[gradio]

[gradio]
gradio<6,>=5.34
gradio-pdf>=0.0.22

[pipeline]
matplotlib<4,>=3.10
ultralytics<9,>=8.3.48
doclayout_yolo==0.0.4
dill<1,>=0.3.8
rapid_table<2.0.0,>=1.0.5
PyYAML<7,>=6.0.2
ftfy<7,>=6.3.1
openai<2,>=1.70.0
shapely<3,>=2.0.7
pyclipper<2,>=1.3.0
omegaconf<3,>=2.3.0
torch!=2.5.0,!=2.5.1,<3,>=2.2.2
torchvision
transformers!=4.51.0,<5.0.0,>=4.49.0
fast-langdetect<0.3.0,>=0.2.3

[pipeline_old_linux]
matplotlib<=3.10.1,>=3.10
ultralytics<=8.3.104,>=8.3.48
doclayout_yolo==0.0.4
dill==0.3.8
PyYAML==6.0.2
ftfy==6.3.1
openai==1.71.0
shapely==2.1.0
pyclipper==1.3.0.post6
omegaconf==2.3.0
albumentations==1.4.20
rapid_table==1.0.3
torch!=2.5.0,!=2.5.1,<3,>=2.2.2
torchvision
transformers!=4.51.0,<5.0.0,>=4.49.0
fast-langdetect<0.3.0,>=0.2.3

[sglang]
sglang[all]<0.4.9,>=0.4.8

[vlm]
transformers>=4.51.1
torch>=2.6.0
accelerate>=1.5.1
pydantic
