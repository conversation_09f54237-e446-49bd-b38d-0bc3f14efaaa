# Copyright (c) Opendatalab. All rights reserved.
import tempfile
import os
import shutil
from pathlib import Path
from loguru import logger


def word_bytes_to_pdf_bytes(file_path):
    """
    将Word文档转换为PDF格式字节数据
    使用docx2pdf库进行转换
    
    Args:
        file_path: Word文档的文件路径 (Path对象或字符串)
        
    Returns:
        bytes: PDF格式的字节数据
    """
    if not isinstance(file_path, Path):
        file_path = Path(file_path)
    
    try:
        from docx2pdf import convert
        
        # 创建临时输出目录
        temp_dir = tempfile.mkdtemp()
        pdf_path = os.path.join(temp_dir, file_path.stem + '.pdf')
        
        logger.info(f"开始转换Word文档: {file_path}")
        
        # 执行转换
        convert(str(file_path), pdf_path)
        
        # 读取PDF字节数据
        with open(pdf_path, 'rb') as pdf_file:
            pdf_bytes = pdf_file.read()
        
        # 清理临时文件
        shutil.rmtree(temp_dir)
        
        logger.info(f"转换成功，PDF大小: {len(pdf_bytes)} bytes")
        return pdf_bytes
        
    except ImportError:
        logger.error("docx2pdf库未安装，请运行: pip install docx2pdf")
        # 转换失败时返回原始字节数据
        with open(file_path, 'rb') as f:
            return f.read()
    except Exception as e:
        logger.error(f"PDF转换失败: {e}")
        # 转换失败时返回原始字节数据
        with open(file_path, 'rb') as f:
            return f.read()


def is_docx2pdf_available():
    """
    检查docx2pdf是否可用
    
    Returns:
        bool: True如果docx2pdf可用，False否则
    """
    try:
        import docx2pdf
        return True
    except ImportError:
        return False


if __name__ == "__main__":
    # 测试代码
    if is_docx2pdf_available():
        print("✓ docx2pdf可用，系统已准备就绪！")
    else:
        print("✗ docx2pdf不可用")
        print("请安装docx2pdf库: pip install docx2pdf")

