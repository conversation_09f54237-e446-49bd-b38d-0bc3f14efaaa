{"file_location": "tests/unittest/test_tools/assets/cli_dev/cli_test_01.pdf", "doc_layout_result": [{"layout_dets": [{"category_id": 1, "poly": [882.4013061523438, 169.93817138671875, 1552.350341796875, 169.93817138671875, 1552.350341796875, 625.8263549804688, 882.4013061523438, 625.8263549804688], "score": 0.999992311000824}, {"category_id": 1, "poly": [882.474853515625, 1450.92822265625, 1551.4490966796875, 1450.92822265625, 1551.4490966796875, 1877.5712890625, 882.474853515625, 1877.5712890625], "score": 0.9999903440475464}, {"category_id": 1, "poly": [881.6513061523438, 626.2058715820312, 1552.1400146484375, 626.2058715820312, 1552.1400146484375, 1450.604736328125, 881.6513061523438, 1450.604736328125], "score": 0.9999856352806091}, {"category_id": 1, "poly": [149.41075134277344, 232.1595001220703, 819.0465087890625, 232.1595001220703, 819.0465087890625, 625.8865356445312, 149.41075134277344, 625.8865356445312], "score": 0.99998539686203}, {"category_id": 1, "poly": [149.3945770263672, 1215.5172119140625, 817.8850708007812, 1215.5172119140625, 817.8850708007812, 1304.873291015625, 149.3945770263672, 1304.873291015625], "score": 0.9999765157699585}, {"category_id": 1, "poly": [882.6979370117188, 1880.13916015625, 1552.15185546875, 1880.13916015625, 1552.15185546875, 2031.339599609375, 882.6979370117188, 2031.339599609375], "score": 0.9999744892120361}, {"category_id": 1, "poly": [148.96054077148438, 743.3055419921875, 818.6231689453125, 743.3055419921875, 818.6231689453125, 1074.2369384765625, 148.96054077148438, 1074.2369384765625], "score": 0.9999669790267944}, {"category_id": 1, "poly": [148.8435516357422, 1791.14306640625, 818.6885375976562, 1791.14306640625, 818.6885375976562, 2030.794189453125, 148.8435516357422, 2030.794189453125], "score": 0.9999618530273438}, {"category_id": 0, "poly": [150.7009735107422, 684.0087890625, 623.5106201171875, 684.0087890625, 623.5106201171875, 717.03662109375, 150.7009735107422, 717.03662109375], "score": 0.9999415278434753}, {"category_id": 8, "poly": [146.48068237304688, 1331.6737060546875, 317.2640075683594, 1331.6737060546875, 317.2640075683594, 1400.1722412109375, 146.48068237304688, 1400.1722412109375], "score": 0.9998958110809326}, {"category_id": 1, "poly": [149.42420959472656, 1430.8782958984375, 818.9042358398438, 1430.8782958984375, 818.9042358398438, 1672.7386474609375, 149.42420959472656, 1672.7386474609375], "score": 0.9998599290847778}, {"category_id": 1, "poly": [149.18746948242188, 172.10252380371094, 818.5662231445312, 172.10252380371094, 818.5662231445312, 230.4594268798828, 149.18746948242188, 230.4594268798828], "score": 0.9997718334197998}, {"category_id": 0, "poly": [149.0175018310547, 1732.1090087890625, 702.1005859375, 1732.1090087890625, 702.1005859375, 1763.6046142578125, 149.0175018310547, 1763.6046142578125], "score": 0.9997085928916931}, {"category_id": 2, "poly": [1519.802490234375, 98.59099578857422, 1551.985107421875, 98.59099578857422, 1551.985107421875, 119.48420715332031, 1519.802490234375, 119.48420715332031], "score": 0.9995552897453308}, {"category_id": 8, "poly": [146.9109649658203, 1100.156494140625, 544.2803344726562, 1100.156494140625, 544.2803344726562, 1184.929443359375, 146.9109649658203, 1184.929443359375], "score": 0.9995207786560059}, {"category_id": 2, "poly": [148.11611938476562, 99.87767791748047, 318.926025390625, 99.87767791748047, 318.926025390625, 120.70393371582031, 148.11611938476562, 120.70393371582031], "score": 0.999351441860199}, {"category_id": 9, "poly": [791.7642211914062, 1130.056396484375, 818.6940307617188, 1130.056396484375, 818.6940307617188, 1161.1080322265625, 791.7642211914062, 1161.1080322265625], "score": 0.9908884763717651}, {"category_id": 9, "poly": [788.37060546875, 1346.8450927734375, 818.5010986328125, 1346.8450927734375, 818.5010986328125, 1377.370361328125, 788.37060546875, 1377.370361328125], "score": 0.9873985052108765}, {"category_id": 14, "poly": [146, 1103, 543, 1103, 543, 1184, 146, 1184], "score": 0.94, "latex": "E\\!\\left(W\\right)\\!=\\!\\frac{E\\!\\left[H^{2}\\right]}{2E\\!\\left[H\\right]}\\!=\\!\\frac{E\\!\\left[H\\right]}{2}\\!\\!\\left(1\\!+\\!\\operatorname{CV}\\!\\left(H\\right)^{2}\\right)"}, {"category_id": 13, "poly": [1196, 354, 1278, 354, 1278, 384, 1196, 384], "score": 0.91, "latex": "p(1-q)"}, {"category_id": 13, "poly": [881, 415, 1020, 415, 1020, 444, 881, 444], "score": 0.91, "latex": "(1-p)(1-q)"}, {"category_id": 14, "poly": [147, 1333, 318, 1333, 318, 1400, 147, 1400], "score": 0.91, "latex": "\\mathrm{CV}\\big(H\\big)\\!=\\!\\frac{\\sigma_{_H}}{E\\big[H\\big]}"}, {"category_id": 13, "poly": [1197, 657, 1263, 657, 1263, 686, 1197, 686], "score": 0.9, "latex": "(1-p)"}, {"category_id": 13, "poly": [213, 1217, 263, 1217, 263, 1244, 213, 1244], "score": 0.88, "latex": "E[X]"}, {"category_id": 13, "poly": [214, 1434, 245, 1434, 245, 1459, 214, 1459], "score": 0.87, "latex": "\\upsigma_{H}"}, {"category_id": 13, "poly": [324, 2002, 373, 2002, 373, 2028, 324, 2028], "score": 0.84, "latex": "30\\%"}, {"category_id": 13, "poly": [1209, 693, 1225, 693, 1225, 717, 1209, 717], "score": 0.83, "latex": "p"}, {"category_id": 13, "poly": [990, 449, 1007, 449, 1007, 474, 990, 474], "score": 0.81, "latex": "p"}, {"category_id": 13, "poly": [346, 1277, 369, 1277, 369, 1301, 346, 1301], "score": 0.81, "latex": "H"}, {"category_id": 13, "poly": [1137, 661, 1154, 661, 1154, 686, 1137, 686], "score": 0.81, "latex": "p"}, {"category_id": 13, "poly": [522, 1432, 579, 1432, 579, 1459, 522, 1459], "score": 0.81, "latex": "H\\left(4\\right)"}, {"category_id": 13, "poly": [944, 540, 962, 540, 962, 565, 944, 565], "score": 0.8, "latex": "p"}, {"category_id": 13, "poly": [1444, 936, 1461, 936, 1461, 961, 1444, 961], "score": 0.79, "latex": "p"}, {"category_id": 13, "poly": [602, 1247, 624, 1247, 624, 1270, 602, 1270], "score": 0.78, "latex": "H"}, {"category_id": 13, "poly": [147, 1247, 167, 1247, 167, 1271, 147, 1271], "score": 0.77, "latex": "X"}, {"category_id": 13, "poly": [210, 1246, 282, 1246, 282, 1274, 210, 1274], "score": 0.77, "latex": "\\mathrm{CV}(H)"}, {"category_id": 13, "poly": [1346, 268, 1361, 268, 1361, 292, 1346, 292], "score": 0.76, "latex": "q"}, {"category_id": 13, "poly": [215, 957, 238, 957, 238, 981, 215, 981], "score": 0.74, "latex": "H"}, {"category_id": 13, "poly": [149, 956, 173, 956, 173, 981, 149, 981], "score": 0.63, "latex": "W"}, {"category_id": 13, "poly": [924, 841, 1016, 841, 1016, 868, 924, 868], "score": 0.56, "latex": "8{\\mathrm{:}}00\\;\\mathrm{a.m}"}, {"category_id": 13, "poly": [956, 871, 1032, 871, 1032, 898, 956, 898], "score": 0.43, "latex": "20\\ \\mathrm{min}"}, {"category_id": 13, "poly": [1082, 781, 1112, 781, 1112, 808, 1082, 808], "score": 0.41, "latex": "(I)"}, {"category_id": 13, "poly": [697, 1821, 734, 1821, 734, 1847, 697, 1847], "score": 0.3, "latex": "1\\,\\mathrm{~h~}"}, {"category_id": 15, "poly": [881.0, 174.0, 1552.0, 174.0, 1552.0, 204.0, 881.0, 204.0], "score": 1.0, "text": "model. They also found that the empirical distributions of passenger"}, {"category_id": 15, "poly": [880.0, 205.0, 1552.0, 205.0, 1552.0, 236.0, 880.0, 236.0], "score": 0.99, "text": "incidence times (by time of day) had peaks just before the respec-"}, {"category_id": 15, "poly": [880.0, 234.0, 1553.0, 234.0, 1553.0, 264.0, 880.0, 264.0], "score": 0.99, "text": "tive average bus departure times. They hypothesized the existence"}, {"category_id": 15, "poly": [881.0, 264.0, 1345.0, 264.0, 1345.0, 296.0, 881.0, 296.0], "score": 0.98, "text": "of three classes of passengers: with proportion"}, {"category_id": 15, "poly": [1362.0, 264.0, 1552.0, 264.0, 1552.0, 296.0, 1362.0, 296.0], "score": 0.95, "text": "passengers whose"}, {"category_id": 15, "poly": [880.0, 295.0, 1552.0, 295.0, 1552.0, 325.0, 880.0, 325.0], "score": 1.0, "text": "time of incidence is causally coincident with that of a bus departure"}, {"category_id": 15, "poly": [880.0, 326.0, 1555.0, 326.0, 1555.0, 355.0, 880.0, 355.0], "score": 0.99, "text": "(e.g., because they saw the approaching bus from their home or a"}, {"category_id": 15, "poly": [881.0, 356.0, 1195.0, 356.0, 1195.0, 388.0, 881.0, 388.0], "score": 0.99, "text": "shop window); with proportion"}, {"category_id": 15, "poly": [1279.0, 356.0, 1553.0, 356.0, 1553.0, 388.0, 1279.0, 388.0], "score": 0.99, "text": ", passengers who time their"}, {"category_id": 15, "poly": [882.0, 388.0, 1552.0, 388.0, 1552.0, 416.0, 882.0, 416.0], "score": 0.99, "text": "arrivals to minimize expected waiting time; and with proportion"}, {"category_id": 15, "poly": [1021.0, 418.0, 1553.0, 418.0, 1553.0, 447.0, 1021.0, 447.0], "score": 1.0, "text": ", passengers who are randomly incident. The authors"}, {"category_id": 15, "poly": [881.0, 448.0, 989.0, 448.0, 989.0, 477.0, 881.0, 477.0], "score": 1.0, "text": "found that"}, {"category_id": 15, "poly": [1008.0, 448.0, 1553.0, 448.0, 1553.0, 477.0, 1008.0, 477.0], "score": 1.0, "text": "was positively correlated with the potential reduction"}, {"category_id": 15, "poly": [880.0, 479.0, 1552.0, 479.0, 1552.0, 507.0, 880.0, 507.0], "score": 1.0, "text": "in waiting time (compared with arriving randomly) that resulted"}, {"category_id": 15, "poly": [882.0, 510.0, 1551.0, 510.0, 1551.0, 536.0, 882.0, 536.0], "score": 0.97, "text": "from knowledge of the timetable and of service reliability. They also"}, {"category_id": 15, "poly": [881.0, 539.0, 943.0, 539.0, 943.0, 568.0, 881.0, 568.0], "score": 1.0, "text": "found"}, {"category_id": 15, "poly": [963.0, 539.0, 1553.0, 539.0, 1553.0, 568.0, 963.0, 568.0], "score": 0.99, "text": "to be higher in the peak commuting periods rather than in"}, {"category_id": 15, "poly": [881.0, 568.0, 1554.0, 568.0, 1554.0, 599.0, 881.0, 599.0], "score": 0.98, "text": "the off-peak periods, indicating more awareness of the timetable or"}, {"category_id": 15, "poly": [881.0, 599.0, 1323.0, 599.0, 1323.0, 627.0, 881.0, 627.0], "score": 0.98, "text": "historical reliability, or both, by commuters."}, {"category_id": 15, "poly": [905.0, 1452.0, 1551.0, 1452.0, 1551.0, 1483.0, 905.0, 1483.0], "score": 0.99, "text": "<PERSON><PERSON> and <PERSON> study the issue in a theoretical context and gener-"}, {"category_id": 15, "poly": [883.0, 1485.0, 1553.0, 1485.0, 1553.0, 1514.0, 883.0, 1514.0], "score": 1.0, "text": "ally agree with the above findings (2). They are primarily concerned"}, {"category_id": 15, "poly": [882.0, 1513.0, 1553.0, 1513.0, 1553.0, 1545.0, 882.0, 1545.0], "score": 0.99, "text": "with the use of data from automatic vehicle-tracking systems to assess"}, {"category_id": 15, "poly": [880.0, 1545.0, 1553.0, 1545.0, 1553.0, 1574.0, 880.0, 1574.0], "score": 0.99, "text": "the impacts of reliability on passenger incidence behavior and wait-"}, {"category_id": 15, "poly": [881.0, 1577.0, 1551.0, 1577.0, 1551.0, 1606.0, 881.0, 1606.0], "score": 0.98, "text": "ing times. They propose that passengers will react to unreliability by"}, {"category_id": 15, "poly": [883.0, 1608.0, 1551.0, 1608.0, 1551.0, 1637.0, 883.0, 1637.0], "score": 1.0, "text": "departing earlier than they would with reliable services. Randomly"}, {"category_id": 15, "poly": [880.0, 1636.0, 1554.0, 1636.0, 1554.0, 1669.0, 880.0, 1669.0], "score": 1.0, "text": "incident unaware passengers will experience unreliability as a more"}, {"category_id": 15, "poly": [882.0, 1669.0, 1553.0, 1669.0, 1553.0, 1697.0, 882.0, 1697.0], "score": 0.99, "text": "dispersed distribution of headways and simply allocate additional"}, {"category_id": 15, "poly": [880.0, 1699.0, 1551.0, 1699.0, 1551.0, 1726.0, 880.0, 1726.0], "score": 0.97, "text": "time to their trip plan to improve the chance of arriving at their des-"}, {"category_id": 15, "poly": [881.0, 1730.0, 1551.0, 1730.0, 1551.0, 1759.0, 881.0, 1759.0], "score": 0.98, "text": "tination on time. Aware passengers, whose incidence is not entirely"}, {"category_id": 15, "poly": [880.0, 1760.0, 1552.0, 1760.0, 1552.0, 1789.0, 880.0, 1789.0], "score": 0.99, "text": "random, will react by timing their incidence somewhat earlier than"}, {"category_id": 15, "poly": [882.0, 1792.0, 1550.0, 1792.0, 1550.0, 1818.0, 882.0, 1818.0], "score": 0.99, "text": "the scheduled departure time to increase their chance of catching the"}, {"category_id": 15, "poly": [883.0, 1823.0, 1552.0, 1823.0, 1552.0, 1849.0, 883.0, 1849.0], "score": 0.99, "text": "desired service. The authors characterize these reactions as the costs"}, {"category_id": 15, "poly": [883.0, 1853.0, 1031.0, 1853.0, 1031.0, 1880.0, 883.0, 1880.0], "score": 0.95, "text": "of unreliability."}, {"category_id": 15, "poly": [907.0, 630.0, 1553.0, 630.0, 1553.0, 658.0, 907.0, 658.0], "score": 1.0, "text": "<PERSON> and <PERSON><PERSON> built on the concept of aware and unaware"}, {"category_id": 15, "poly": [881.0, 662.0, 1136.0, 662.0, 1136.0, 690.0, 881.0, 690.0], "score": 0.99, "text": "passengers of proportions"}, {"category_id": 15, "poly": [1155.0, 662.0, 1196.0, 662.0, 1196.0, 690.0, 1155.0, 690.0], "score": 1.0, "text": "and"}, {"category_id": 15, "poly": [1264.0, 662.0, 1553.0, 662.0, 1553.0, 690.0, 1264.0, 690.0], "score": 0.99, "text": ",respectively. They proposed"}, {"category_id": 15, "poly": [881.0, 692.0, 1208.0, 692.0, 1208.0, 719.0, 881.0, 719.0], "score": 0.99, "text": "a utility-based model to estimate"}, {"category_id": 15, "poly": [1226.0, 692.0, 1552.0, 692.0, 1552.0, 719.0, 1226.0, 719.0], "score": 1.0, "text": "and the distribution of incidence"}, {"category_id": 15, "poly": [880.0, 721.0, 1554.0, 721.0, 1554.0, 751.0, 880.0, 751.0], "score": 0.99, "text": "times, and thus the mean waiting time, of aware passengers over"}, {"category_id": 15, "poly": [880.0, 752.0, 1553.0, 752.0, 1553.0, 780.0, 880.0, 780.0], "score": 0.98, "text": "a given headway as a function of the headway and reliability of"}, {"category_id": 15, "poly": [880.0, 782.0, 1081.0, 782.0, 1081.0, 812.0, 880.0, 812.0], "score": 0.99, "text": "bus departure times"}, {"category_id": 15, "poly": [1113.0, 782.0, 1552.0, 782.0, 1552.0, 812.0, 1113.0, 812.0], "score": 0.99, "text": ". They observed seven bus stops in Chicago,"}, {"category_id": 15, "poly": [882.0, 813.0, 1553.0, 813.0, 1553.0, 841.0, 882.0, 841.0], "score": 0.98, "text": "Illinois, each served by a single (different) bus route, between 6:00"}, {"category_id": 15, "poly": [882.0, 844.0, 923.0, 844.0, 923.0, 871.0, 882.0, 871.0], "score": 1.0, "text": "and"}, {"category_id": 15, "poly": [1017.0, 844.0, 1550.0, 844.0, 1550.0, 871.0, 1017.0, 871.0], "score": 0.97, "text": ".for 5 to 10 days each. The bus routes had headways"}, {"category_id": 15, "poly": [882.0, 874.0, 955.0, 874.0, 955.0, 902.0, 882.0, 902.0], "score": 0.95, "text": "of 5to"}, {"category_id": 15, "poly": [1033.0, 874.0, 1553.0, 874.0, 1553.0, 902.0, 1033.0, 902.0], "score": 0.98, "text": "and a range of reliabilities. The authors found that"}, {"category_id": 15, "poly": [882.0, 906.0, 1553.0, 906.0, 1553.0, 933.0, 882.0, 933.0], "score": 0.99, "text": "actual average waiting time was substantially less than predicted"}, {"category_id": 15, "poly": [881.0, 935.0, 1443.0, 935.0, 1443.0, 963.0, 881.0, 963.0], "score": 1.0, "text": "by the random incidence model. They estimated that"}, {"category_id": 15, "poly": [1462.0, 935.0, 1553.0, 935.0, 1553.0, 963.0, 1462.0, 963.0], "score": 0.96, "text": "was not"}, {"category_id": 15, "poly": [881.0, 966.0, 1552.0, 966.0, 1552.0, 994.0, 881.0, 994.0], "score": 0.98, "text": "statistically significantly different from 1.0, which they explain by"}, {"category_id": 15, "poly": [880.0, 994.0, 1552.0, 994.0, 1552.0, 1025.0, 880.0, 1025.0], "score": 0.99, "text": "the fact that all observations were taken during peak commuting"}, {"category_id": 15, "poly": [880.0, 1027.0, 1552.0, 1027.0, 1552.0, 1054.0, 880.0, 1054.0], "score": 0.99, "text": "times. Their model predicts that the longer the headway and the"}, {"category_id": 15, "poly": [881.0, 1058.0, 1554.0, 1058.0, 1554.0, 1086.0, 881.0, 1086.0], "score": 0.99, "text": "more reliable the departures, the more peaked the distribution of"}, {"category_id": 15, "poly": [881.0, 1088.0, 1553.0, 1088.0, 1553.0, 1115.0, 881.0, 1115.0], "score": 0.98, "text": "incidence times will be and the closer that peak will be to the next"}, {"category_id": 15, "poly": [882.0, 1119.0, 1552.0, 1119.0, 1552.0, 1148.0, 882.0, 1148.0], "score": 1.0, "text": "scheduled departure time. This prediction demonstrates what they"}, {"category_id": 15, "poly": [882.0, 1149.0, 1552.0, 1149.0, 1552.0, 1176.0, 882.0, 1176.0], "score": 0.99, "text": "refer to as a safety margin that passengers add to reduce the chance"}, {"category_id": 15, "poly": [883.0, 1181.0, 1552.0, 1181.0, 1552.0, 1206.0, 883.0, 1206.0], "score": 0.98, "text": "of missing their bus when the service is known to be somewhat"}, {"category_id": 15, "poly": [882.0, 1210.0, 1551.0, 1210.0, 1551.0, 1238.0, 882.0, 1238.0], "score": 0.98, "text": "unreliable. Such a safety margin can also result from unreliability in"}, {"category_id": 15, "poly": [881.0, 1242.0, 1553.0, 1242.0, 1553.0, 1269.0, 881.0, 1269.0], "score": 0.99, "text": "passengers' journeys to the public transport stop or station. <PERSON>"}, {"category_id": 15, "poly": [882.0, 1271.0, 1553.0, 1271.0, 1553.0, 1299.0, 882.0, 1299.0], "score": 0.99, "text": "and <PERSON><PERSON> conclude from their model that the random incidence"}, {"category_id": 15, "poly": [880.0, 1301.0, 1551.0, 1301.0, 1551.0, 1331.0, 880.0, 1331.0], "score": 0.99, "text": "model underestimates the waiting time benefits of improving reli-"}, {"category_id": 15, "poly": [882.0, 1332.0, 1552.0, 1332.0, 1552.0, 1362.0, 882.0, 1362.0], "score": 0.99, "text": "ability and overestimates the waiting time benefits of increasing ser-"}, {"category_id": 15, "poly": [883.0, 1363.0, 1552.0, 1363.0, 1552.0, 1392.0, 883.0, 1392.0], "score": 0.99, "text": "vice frequency. This is because as reliability increases passengers"}, {"category_id": 15, "poly": [882.0, 1394.0, 1552.0, 1394.0, 1552.0, 1422.0, 882.0, 1422.0], "score": 0.99, "text": "can better predict departure times and so can time their incidence to"}, {"category_id": 15, "poly": [882.0, 1423.0, 1159.0, 1423.0, 1159.0, 1452.0, 882.0, 1452.0], "score": 0.99, "text": "decrease their waiting time."}, {"category_id": 15, "poly": [175.0, 235.0, 819.0, 235.0, 819.0, 264.0, 175.0, 264.0], "score": 0.99, "text": "After briefly introducing the random incidence model, which is"}, {"category_id": 15, "poly": [149.0, 265.0, 818.0, 265.0, 818.0, 295.0, 149.0, 295.0], "score": 0.98, "text": "often assumed to hold at short headways, the balance of this section"}, {"category_id": 15, "poly": [148.0, 298.0, 818.0, 298.0, 818.0, 324.0, 148.0, 324.0], "score": 0.98, "text": "reviews six studies of passenger incidence behavior that are moti-"}, {"category_id": 15, "poly": [148.0, 327.0, 818.0, 327.0, 818.0, 356.0, 148.0, 356.0], "score": 1.0, "text": "vated by understanding the relationships between service headway,"}, {"category_id": 15, "poly": [146.0, 355.0, 820.0, 355.0, 820.0, 388.0, 146.0, 388.0], "score": 0.99, "text": "service reliability, passenger incidence behavior, and passenger"}, {"category_id": 15, "poly": [149.0, 388.0, 818.0, 388.0, 818.0, 414.0, 149.0, 414.0], "score": 1.0, "text": "waiting time in a more nuanced fashion than is embedded in the"}, {"category_id": 15, "poly": [149.0, 419.0, 818.0, 419.0, 818.0, 445.0, 149.0, 445.0], "score": 1.0, "text": "random incidence assumption (2). Three of these studies depend on"}, {"category_id": 15, "poly": [147.0, 447.0, 818.0, 447.0, 818.0, 477.0, 147.0, 477.0], "score": 0.99, "text": "manually collected data, two studies use data from AFC systems,"}, {"category_id": 15, "poly": [148.0, 479.0, 819.0, 479.0, 819.0, 507.0, 148.0, 507.0], "score": 0.99, "text": "and one study analyzes the issue purely theoretically. These studies"}, {"category_id": 15, "poly": [147.0, 509.0, 819.0, 509.0, 819.0, 537.0, 147.0, 537.0], "score": 0.99, "text": "reveal much about passenger incidence behavior, but all are found"}, {"category_id": 15, "poly": [147.0, 538.0, 820.0, 538.0, 820.0, 567.0, 147.0, 567.0], "score": 0.99, "text": "to be limited in their general applicability by the methods with"}, {"category_id": 15, "poly": [150.0, 569.0, 818.0, 569.0, 818.0, 597.0, 150.0, 597.0], "score": 0.99, "text": "which they collect information about passengers and the services"}, {"category_id": 15, "poly": [147.0, 599.0, 458.0, 599.0, 458.0, 630.0, 147.0, 630.0], "score": 1.0, "text": "those passengers intend to use."}, {"category_id": 15, "poly": [150.0, 1219.0, 212.0, 1219.0, 212.0, 1247.0, 150.0, 1247.0], "score": 1.0, "text": "where"}, {"category_id": 15, "poly": [264.0, 1219.0, 817.0, 1219.0, 817.0, 1247.0, 264.0, 1247.0], "score": 0.99, "text": "is the probabilistic expectation of some random variable"}, {"category_id": 15, "poly": [168.0, 1248.0, 209.0, 1248.0, 209.0, 1275.0, 168.0, 1275.0], "score": 1.0, "text": "and"}, {"category_id": 15, "poly": [283.0, 1248.0, 601.0, 1248.0, 601.0, 1275.0, 283.0, 1275.0], "score": 0.97, "text": "is the coefficient of variation of"}, {"category_id": 15, "poly": [625.0, 1248.0, 818.0, 1248.0, 818.0, 1275.0, 625.0, 1275.0], "score": 0.96, "text": ".a unitless measure"}, {"category_id": 15, "poly": [148.0, 1277.0, 345.0, 1277.0, 345.0, 1307.0, 148.0, 1307.0], "score": 0.97, "text": "of the variability of"}, {"category_id": 15, "poly": [370.0, 1277.0, 477.0, 1277.0, 477.0, 1307.0, 370.0, 1307.0], "score": 0.99, "text": "defined as"}, {"category_id": 15, "poly": [906.0, 1883.0, 1552.0, 1883.0, 1552.0, 1910.0, 906.0, 1910.0], "score": 0.98, "text": "<PERSON><PERSON><PERSON> et al. continued with the analysis of manually collected"}, {"category_id": 15, "poly": [880.0, 1909.0, 1552.0, 1909.0, 1552.0, 1945.0, 880.0, 1945.0], "score": 0.99, "text": "data on actual passenger behavior (6). They use the language"}, {"category_id": 15, "poly": [883.0, 1945.0, 1552.0, 1945.0, 1552.0, 1972.0, 883.0, 1972.0], "score": 0.99, "text": "of probability to describe two classes of passengers. The first is"}, {"category_id": 15, "poly": [881.0, 1973.0, 1552.0, 1973.0, 1552.0, 2003.0, 881.0, 2003.0], "score": 1.0, "text": "timetable-dependent passengers (i.e., the aware passengers), whose"}, {"category_id": 15, "poly": [881.0, 2006.0, 1552.0, 2006.0, 1552.0, 2033.0, 881.0, 2033.0], "score": 1.0, "text": "incidence behavior is affected by awareness (possibly gained"}, {"category_id": 15, "poly": [149.0, 748.0, 817.0, 748.0, 817.0, 774.0, 149.0, 774.0], "score": 1.0, "text": "One characterization of passenger incidence behavior is that of ran-"}, {"category_id": 15, "poly": [148.0, 777.0, 818.0, 777.0, 818.0, 806.0, 148.0, 806.0], "score": 0.99, "text": "dom incidence (3). The key assumption underlying the random inci-"}, {"category_id": 15, "poly": [148.0, 807.0, 818.0, 807.0, 818.0, 836.0, 148.0, 836.0], "score": 0.99, "text": "dence model is that the process of passenger arrivals to the public"}, {"category_id": 15, "poly": [148.0, 837.0, 819.0, 837.0, 819.0, 866.0, 148.0, 866.0], "score": 0.99, "text": "transport service is independent from the vehicle departure process"}, {"category_id": 15, "poly": [148.0, 868.0, 818.0, 868.0, 818.0, 897.0, 148.0, 897.0], "score": 1.0, "text": "of the service. This implies that passengers become incident to the"}, {"category_id": 15, "poly": [149.0, 899.0, 817.0, 899.0, 817.0, 925.0, 149.0, 925.0], "score": 0.99, "text": "service at a random time, and thus the instantaneous rate of passen-"}, {"category_id": 15, "poly": [148.0, 928.0, 820.0, 928.0, 820.0, 957.0, 148.0, 957.0], "score": 1.0, "text": "ger arrivals to the service is uniform over a given period of time. Let"}, {"category_id": 15, "poly": [174.0, 956.0, 214.0, 956.0, 214.0, 990.0, 174.0, 990.0], "score": 1.0, "text": "and"}, {"category_id": 15, "poly": [239.0, 956.0, 818.0, 956.0, 818.0, 990.0, 239.0, 990.0], "score": 0.99, "text": "be random variables representing passenger waiting times"}, {"category_id": 15, "poly": [148.0, 988.0, 818.0, 988.0, 818.0, 1016.0, 148.0, 1016.0], "score": 1.0, "text": "and service headways, respectively. Under the random incidence"}, {"category_id": 15, "poly": [149.0, 1019.0, 818.0, 1019.0, 818.0, 1048.0, 149.0, 1048.0], "score": 0.98, "text": "assumption and the assumption that vehicle capacity is not a binding"}, {"category_id": 15, "poly": [149.0, 1050.0, 726.0, 1050.0, 726.0, 1076.0, 149.0, 1076.0], "score": 0.99, "text": "constraint, a classic result of transportation science is that"}, {"category_id": 15, "poly": [146.0, 1793.0, 818.0, 1793.0, 818.0, 1822.0, 146.0, 1822.0], "score": 0.98, "text": " <PERSON><PERSON><PERSON> and <PERSON> studied bus passenger incidence in South"}, {"category_id": 15, "poly": [147.0, 1825.0, 696.0, 1825.0, 696.0, 1852.0, 147.0, 1852.0], "score": 0.97, "text": "London suburbs (5). They observed 10 bus stops for"}, {"category_id": 15, "poly": [735.0, 1825.0, 817.0, 1825.0, 817.0, 1852.0, 735.0, 1852.0], "score": 1.0, "text": "perday"}, {"category_id": 15, "poly": [148.0, 1855.0, 819.0, 1855.0, 819.0, 1881.0, 148.0, 1881.0], "score": 1.0, "text": "over 8 days, recording the times of passenger incidence and actual"}, {"category_id": 15, "poly": [148.0, 1884.0, 819.0, 1884.0, 819.0, 1912.0, 148.0, 1912.0], "score": 0.98, "text": "and scheduled bus departures. They limited their stop selection to"}, {"category_id": 15, "poly": [146.0, 1913.0, 819.0, 1913.0, 819.0, 1945.0, 146.0, 1945.0], "score": 1.0, "text": "those served by only a single bus route with a single service pat-"}, {"category_id": 15, "poly": [147.0, 1945.0, 819.0, 1945.0, 819.0, 1974.0, 147.0, 1974.0], "score": 0.98, "text": "tern so as to avoid ambiguity about which service a passenger was"}, {"category_id": 15, "poly": [147.0, 1972.0, 820.0, 1972.0, 820.0, 2006.0, 147.0, 2006.0], "score": 0.98, "text": "waiting for. The authors found that the actual average passenger"}, {"category_id": 15, "poly": [149.0, 2005.0, 323.0, 2005.0, 323.0, 2033.0, 149.0, 2033.0], "score": 0.96, "text": "waitingtimewas"}, {"category_id": 15, "poly": [374.0, 2005.0, 819.0, 2005.0, 819.0, 2033.0, 374.0, 2033.0], "score": 1.0, "text": "less than predicted by the random incidence"}, {"category_id": 15, "poly": [148.0, 686.0, 625.0, 686.0, 625.0, 721.0, 148.0, 721.0], "score": 0.99, "text": "Random Passenger Incidence Behavior"}, {"category_id": 15, "poly": [151.0, 1434.0, 213.0, 1434.0, 213.0, 1462.0, 151.0, 1462.0], "score": 0.99, "text": "where"}, {"category_id": 15, "poly": [246.0, 1434.0, 521.0, 1434.0, 521.0, 1462.0, 246.0, 1462.0], "score": 0.98, "text": "is the standard deviation of"}, {"category_id": 15, "poly": [580.0, 1434.0, 816.0, 1434.0, 816.0, 1462.0, 580.0, 1462.0], "score": 0.96, "text": ".The second expression"}, {"category_id": 15, "poly": [148.0, 1466.0, 819.0, 1466.0, 819.0, 1493.0, 148.0, 1493.0], "score": 0.99, "text": "in Equation 1 is particularly useful because it expresses the mean"}, {"category_id": 15, "poly": [146.0, 1496.0, 819.0, 1496.0, 819.0, 1525.0, 146.0, 1525.0], "score": 0.99, "text": "passenger waiting time as the sum of two components: the waiting"}, {"category_id": 15, "poly": [148.0, 1526.0, 818.0, 1526.0, 818.0, 1553.0, 148.0, 1553.0], "score": 0.98, "text": "time caused by the mean headway (i.e., the reciprocal of service fre-"}, {"category_id": 15, "poly": [147.0, 1557.0, 819.0, 1557.0, 819.0, 1584.0, 147.0, 1584.0], "score": 0.99, "text": "quency) and the waiting time caused by the variability of the head-"}, {"category_id": 15, "poly": [148.0, 1588.0, 818.0, 1588.0, 818.0, 1612.0, 148.0, 1612.0], "score": 0.97, "text": "ways (which is one measure of service reliability). When the service"}, {"category_id": 15, "poly": [148.0, 1617.0, 817.0, 1617.0, 817.0, 1644.0, 148.0, 1644.0], "score": 1.0, "text": "is perfectly reliable with constant headways, the mean waiting time"}, {"category_id": 15, "poly": [148.0, 1646.0, 472.0, 1646.0, 472.0, 1677.0, 148.0, 1677.0], "score": 0.99, "text": "will be simply half the headway."}, {"category_id": 15, "poly": [151.0, 176.0, 817.0, 176.0, 817.0, 204.0, 151.0, 204.0], "score": 0.99, "text": "dependent on the service headway and the reliability of the departure"}, {"category_id": 15, "poly": [147.0, 205.0, 652.0, 205.0, 652.0, 236.0, 147.0, 236.0], "score": 0.99, "text": "time of the service to which passengers are incident."}, {"category_id": 15, "poly": [149.0, 1735.0, 702.0, 1735.0, 702.0, 1767.0, 149.0, 1767.0], "score": 0.98, "text": "More Behaviorally Realistic Incidence Models"}, {"category_id": 15, "poly": [1519.0, 98.0, 1554.0, 98.0, 1554.0, 125.0, 1519.0, 125.0], "score": 1.0, "text": "53"}, {"category_id": 15, "poly": [148.0, 98.0, 322.0, 98.0, 322.0, 123.0, 148.0, 123.0], "score": 1.0, "text": "<PERSON><PERSON><PERSON> and <PERSON>"}], "page_info": {"page_no": 0, "height": 2200, "width": 1700}}]}